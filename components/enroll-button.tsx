"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { cn } from "@/lib/utils"

export default function EnrollButton() {
  const [visible, setVisible] = useState(false)

  useEffect(() => {
    const handleScroll = () => {
      setVisible(window.scrollY > 300)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <div
      className={cn(
        "fixed bottom-6 right-6 z-50 transition-all duration-300 transform",
        visible ? "translate-y-0 opacity-100" : "translate-y-10 opacity-0",
      )}
    >
      <Button
        asChild
        size="lg"
        className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white shadow-lg rounded-full px-6"
      >
        <Link href="/admissions">Enroll Now</Link>
      </Button>
    </div>
  )
}
