"use client"

import { useState, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, Quote } from "lucide-react"
import Image from "next/image"
import { cn } from "@/lib/utils"

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "Computer Science Graduate",
    image: "/placeholder.svg?height=200&width=200",
    quote:
      "TISTC provided me with the perfect blend of theoretical knowledge and practical skills. The hands-on approach to learning prepared me for my career in software development.",
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "Data Analytics Professional",
    image: "/placeholder.svg?height=200&width=200",
    quote:
      "The professional training program at TISTC transformed my career. The instructors are industry experts who bring real-world experience to the classroom.",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    role: "Electrical Engineering Student",
    image: "/placeholder.svg?height=200&width=200",
    quote:
      "The state-of-the-art labs and equipment at TISTC make learning engaging and practical. I'm confident I'll graduate with the skills needed to succeed in my field.",
  },
  {
    id: 4,
    name: "Tech Innovations Ltd",
    role: "Corporate Training Partner",
    image: "/placeholder.svg?height=200&width=200",
    quote:
      "We've partnered with TISTC for our staff training needs, and the results have been exceptional. Their tailored programs address our specific requirements and challenges.",
  },
]

export default function TestimonialsSlider() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1))
  }

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1))
  }

  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
    // Resume auto-play after 5 seconds of inactivity
    setTimeout(() => setIsAutoPlaying(true), 5000)
  }

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isAutoPlaying) {
      interval = setInterval(() => {
        nextSlide()
      }, 5000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isAutoPlaying, currentIndex])

  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">What Our Students Say</h2>
          <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Hear from our students and partners about their experiences at TISTC.
          </p>
        </div>

        <div className="relative max-w-4xl mx-auto">
          <div className="overflow-hidden">
            <div
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: `translateX(-${currentIndex * 100}%)` }}
            >
              {testimonials.map((testimonial) => (
                <div key={testimonial.id} className="w-full flex-shrink-0 px-4">
                  <Card className="bg-gray-50 dark:bg-gray-900 border-none shadow-lg">
                    <CardContent className="p-8">
                      <div className="flex flex-col md:flex-row items-center gap-6">
                        <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-green-500 flex-shrink-0">
                          <Image
                            src={testimonial.image || "/placeholder.svg"}
                            alt={testimonial.name}
                            fill
                            className="object-cover"
                          />
                        </div>
                        <div className="flex-1">
                          <Quote className="h-8 w-8 text-green-500 mb-4" />
                          <p className="text-gray-700 dark:text-gray-300 text-lg italic mb-4">"{testimonial.quote}"</p>
                          <div>
                            <h4 className="font-bold text-xl">{testimonial.name}</h4>
                            <p className="text-green-600 dark:text-green-400">{testimonial.role}</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>

          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-1/2 bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 focus:outline-none z-10"
            aria-label="Previous testimonial"
          >
            <ChevronLeft size={24} />
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-1/2 bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 focus:outline-none z-10"
            aria-label="Next testimonial"
          >
            <ChevronRight size={24} />
          </button>

          <div className="flex justify-center mt-8 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={cn(
                  "h-3 w-3 rounded-full transition-all",
                  currentIndex === index
                    ? "bg-green-600 w-6"
                    : "bg-gray-300 dark:bg-gray-600 hover:bg-green-400 dark:hover:bg-green-500",
                )}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
