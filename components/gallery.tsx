"use client"

import { useState } from "react"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { X, ChevronLeft, ChevronRight } from "lucide-react"

const galleryImages = [
  {
    id: 1,
    src: "/images/modern-school.png",
    alt: "TISTC Modern Campus Building",
    title: "Modern Campus Building",
    description: "Our state-of-the-art main building houses classrooms, laboratories, and administrative offices.",
  },
  {
    id: 2,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Computer Laboratory",
    title: "Computer Laboratory",
    description: "Fully equipped computer labs with the latest hardware and software for hands-on learning.",
  },
  {
    id: 3,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Engineering Workshop",
    title: "Engineering Workshop",
    description: "Professional-grade workshop facilities for mechanical and electrical engineering students.",
  },
  {
    id: 4,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Science Laboratory",
    title: "Science Laboratory",
    description: "Well-equipped science labs for physics, chemistry, and biology experiments.",
  },
  {
    id: 5,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Fashion Design Studio",
    title: "Fashion Design Studio",
    description: "Creative spaces with modern equipment for fashion design and garment making.",
  },
  {
    id: 6,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Library and Study Area",
    title: "Library and Study Area",
    description: "Quiet study spaces and extensive library resources for academic research.",
  },
  {
    id: 7,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Student Common Area",
    title: "Student Common Area",
    description: "Comfortable spaces for students to relax, collaborate, and socialize.",
  },
  {
    id: 8,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Welding and Fabrication Shop",
    title: "Welding and Fabrication Shop",
    description: "Professional welding and fabrication facilities with safety equipment.",
  },
  {
    id: 9,
    src: "/placeholder.svg?height=600&width=800",
    alt: "Classroom Environment",
    title: "Modern Classrooms",
    description: "Interactive classrooms equipped with smart boards and modern teaching aids.",
  },
]

interface GalleryProps {
  showTitle?: boolean
  maxImages?: number
}

export default function Gallery({ showTitle = true, maxImages }: GalleryProps) {
  const [selectedImage, setSelectedImage] = useState<number | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  const displayImages = maxImages ? galleryImages.slice(0, maxImages) : galleryImages

  const openModal = (index: number) => {
    setCurrentIndex(index)
    setSelectedImage(galleryImages[index].id)
    document.body.style.overflow = "hidden"
  }

  const closeModal = () => {
    setSelectedImage(null)
    document.body.style.overflow = "unset"
  }

  const nextImage = () => {
    const nextIndex = (currentIndex + 1) % galleryImages.length
    setCurrentIndex(nextIndex)
    setSelectedImage(galleryImages[nextIndex].id)
  }

  const prevImage = () => {
    const prevIndex = currentIndex === 0 ? galleryImages.length - 1 : currentIndex - 1
    setCurrentIndex(prevIndex)
    setSelectedImage(galleryImages[prevIndex].id)
  }

  const handleKeyDown = (e: KeyboardEvent) => {
    if (selectedImage) {
      if (e.key === "Escape") closeModal()
      if (e.key === "ArrowRight") nextImage()
      if (e.key === "ArrowLeft") prevImage()
    }
  }

  // Add keyboard event listeners
  useState(() => {
    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  })

  return (
    <section className="py-16 bg-white dark:bg-gray-800">
      <div className="container mx-auto px-4">
        {showTitle && (
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Campus Gallery</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Take a visual tour of our modern facilities, laboratories, and learning spaces designed to provide the
              best educational experience.
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayImages.map((image, index) => (
            <Card
              key={image.id}
              className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none cursor-pointer group"
              onClick={() => openModal(index)}
            >
              <div className="relative h-64 overflow-hidden">
                <Image
                  src={image.src || "/placeholder.svg"}
                  alt={image.alt}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-white/90 dark:bg-gray-800/90 px-4 py-2 rounded-lg">
                      <p className="text-sm font-medium text-gray-800 dark:text-gray-200">Click to view</p>
                    </div>
                  </div>
                </div>
              </div>
              <CardContent className="p-4">
                <h3 className="font-bold text-lg mb-2">{image.title}</h3>
                <p className="text-gray-600 dark:text-gray-300 text-sm">{image.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {maxImages && galleryImages.length > maxImages && (
          <div className="text-center mt-8">
            <Button asChild className="bg-green-600 hover:bg-green-700 text-white">
              <a href="/gallery">View All Photos</a>
            </Button>
          </div>
        )}
      </div>

      {/* Modal */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/90 backdrop-blur-sm">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {/* Close button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-4 right-4 z-10 text-white hover:bg-white/20 rounded-full"
              onClick={closeModal}
            >
              <X size={24} />
            </Button>

            {/* Previous button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full"
              onClick={prevImage}
            >
              <ChevronLeft size={32} />
            </Button>

            {/* Next button */}
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 text-white hover:bg-white/20 rounded-full"
              onClick={nextImage}
            >
              <ChevronRight size={32} />
            </Button>

            {/* Image */}
            <div className="relative max-w-5xl max-h-[90vh] w-full h-full">
              <Image
                src={galleryImages[currentIndex].src || "/placeholder.svg"}
                alt={galleryImages[currentIndex].alt}
                fill
                className="object-contain"
                priority
              />
            </div>

            {/* Image info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black/70 text-white p-4 rounded-lg">
              <h3 className="font-bold text-xl mb-2">{galleryImages[currentIndex].title}</h3>
              <p className="text-gray-200">{galleryImages[currentIndex].description}</p>
              <p className="text-gray-400 text-sm mt-2">
                {currentIndex + 1} of {galleryImages.length}
              </p>
            </div>
          </div>
        </div>
      )}
    </section>
  )
}
