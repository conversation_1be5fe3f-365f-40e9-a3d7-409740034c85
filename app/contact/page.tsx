import Image from "next/image"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Mail, Phone, MapPin, Clock, MessageSquare } from "lucide-react"

export const metadata: Metadata = {
  title: "Contact Us - TISTC | Get in Touch",
  description: "Contact TISTC for inquiries about admissions, programs, or general information. Visit us at 242 Abuloma Road, Port Harcourt or call 09160007864.",
  keywords: "contact TISTC, Port Harcourt technical college, admissions inquiry, campus visit, 09160007864",
  openGraph: {
    title: "Contact Us - TISTC | Get in Touch",
    description: "Contact TISTC for inquiries about admissions, programs, or general information. Visit us at 242 Abuloma Road, Port Harcourt.",
    images: ['/images/about_img/IMG_1927.JPG'],
  },
}

export default function ContactPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">
              Have Questions? We're{" "}
              <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent animate-glow">
                Here to Help
              </span>
            </h1>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-8 animate-fade-in-up animate-delay-200">
              Whether you're seeking admission, exploring our programs, or simply want to learn more, the TISTC team is
              happy to assist you.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-green-500 to-green-600"></div>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-4 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                    <MapPin size={28} />
                  </div>
                </div>
                <h3 className="font-bold text-xl mb-2">Visit Us</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-2">
                  Tobinsco International Science and Technical College (TISTC)
                </p>
                <p className="text-gray-600 dark:text-gray-300 mb-2">242 Abuloma Road, beside Techno Oil,</p>
                <p className="text-gray-600 dark:text-gray-300 mb-2">Abuloma, Port Harcourt, Rivers State, Nigeria</p>
                <Button
                  asChild
                  variant="outline"
                  className="mt-4 border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400 dark:hover:bg-green-900/20"
                >
                  <a href="https://maps.google.com" target="_blank" rel="noopener noreferrer">
                    View on Map
                  </a>
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-red-500 to-red-600"></div>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-4 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400">
                    <Phone size={28} />
                  </div>
                </div>
                <h3 className="font-bold text-xl mb-2">Call Us</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-2">Main Line: 09160007864</p>
                <p className="text-gray-600 dark:text-gray-300 mb-2">Admissions Office: +234 ************</p>
                <Button
                  asChild
                  variant="outline"
                  className="mt-4 border-red-600 text-red-600 hover:bg-red-50 dark:border-red-400 dark:text-red-400 dark:hover:bg-red-900/20"
                >
                  <a href="tel:09160007864">Call Now</a>
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-600"></div>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-4 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                    <Mail size={28} />
                  </div>
                </div>
                <h3 className="font-bold text-xl mb-2">Email Us</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-2">General Inquiries: <EMAIL></p>
                <p className="text-gray-600 dark:text-gray-300 mb-2">Admissions: <EMAIL></p>
                <Button
                  asChild
                  variant="outline"
                  className="mt-4 border-blue-600 text-blue-600 hover:bg-blue-50 dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-900/20"
                >
                  <a href="mailto:<EMAIL>">Send an Email</a>
                </Button>
              </CardContent>
            </Card>
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-purple-500 to-purple-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                    <Clock size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Office Hours</h3>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Monday – Friday:</span>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">8:00 AM – 5:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Saturday:</span>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">9:00 AM – 1:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Sunday:</span>
                    <span className="text-gray-800 dark:text-gray-200 font-medium">Closed</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-yellow-500 to-yellow-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                    <MessageSquare size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Live Chat</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Need quick assistance? Chat with our support team during business hours.
                </p>
                <Button
                  asChild
                  className="bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-white"
                >
                  <a href="#chat">Start Chat</a>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Form and Map */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-12">
            <div className="lg:w-1/2">
              <h2 className="text-3xl font-bold mb-6">
                Send Us a{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-red-600 dark:from-green-400 dark:to-red-400">
                  Message
                </span>
              </h2>

              <Card className="bg-white dark:bg-gray-800 shadow-lg border-none">
                <CardContent className="p-6">
                  <form className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <label htmlFor="name" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Full Name *
                        </label>
                        <Input
                          id="name"
                          placeholder="Enter your full name"
                          className="bg-gray-50 dark:bg-gray-700"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <label htmlFor="email" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Email Address *
                        </label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your email address"
                          className="bg-gray-50 dark:bg-gray-700"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="subject" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Subject *
                      </label>
                      <Input
                        id="subject"
                        placeholder="Enter the subject of your message"
                        className="bg-gray-50 dark:bg-gray-700"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="department" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Department
                      </label>
                      <Select>
                        <SelectTrigger className="bg-gray-50 dark:bg-gray-700">
                          <SelectValue placeholder="Select a department" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">General Inquiry</SelectItem>
                          <SelectItem value="admissions">Admissions</SelectItem>
                          <SelectItem value="academic">Academic Programs</SelectItem>
                          <SelectItem value="professional">Professional Training</SelectItem>
                          <SelectItem value="finance">Finance & Billing</SelectItem>
                          <SelectItem value="support">Technical Support</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="message" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Message *
                      </label>
                      <Textarea
                        id="message"
                        placeholder="Type your message here"
                        className="bg-gray-50 dark:bg-gray-700 min-h-[150px]"
                        required
                      />
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                    >
                      Send Message
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            <div className="lg:w-1/2">
              <h2 className="text-3xl font-bold mb-6">
                Getting to{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-red-600 dark:from-green-400 dark:to-red-400">
                  TISTC
                </span>
              </h2>

              <div className="relative h-[300px] w-full rounded-lg overflow-hidden shadow-xl mb-6">
                <Image src="/images/about_img/IMG_1928.JPG" alt="TISTC Location" fill className="object-cover" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
                    <p className="font-bold">TISTC Campus</p>
                    <p className="text-sm text-gray-600 dark:text-gray-300">242 Abuloma Road, Port Harcourt</p>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                  <h3 className="font-bold text-xl mb-4">By Public Transport</h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-2">
                    TISTC is easily accessible via public transit.
                  </p>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                    <li>• Nearest bus stop: Techno Oil Junction (2 mins walk)</li>
                    <li>• Served by several local bus and tricycle (keke) routes</li>
                  </ul>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                  <h3 className="font-bold text-xl mb-4">By Car</h3>
                  <ul className="space-y-1 text-gray-600 dark:text-gray-300">
                    <li>• 15–20 minutes from Port Harcourt city center</li>
                    <li>• Free visitor parking for up to 2 hours</li>
                    <li>• Parking entrance via Abuloma Road</li>
                  </ul>
                </div>

                <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
                  <h3 className="font-bold text-xl mb-4">From the Airport</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Port Harcourt International Airport is approximately 30 minutes away by car. Taxis and ride-sharing
                    services are readily available.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Campus Tours */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Book a Campus Tour</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Experience TISTC firsthand. Our guided campus tours offer a perfect opportunity to explore our facilities,
              meet faculty, and get a feel for student life.
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <Card className="bg-white dark:bg-gray-800 shadow-lg border-none">
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h3 className="font-bold text-xl mb-4">Tour Schedule</h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• Monday & Wednesday: 10:00 AM</li>
                      <li>• Friday: 2:00 PM</li>
                      <li>• First Saturday of each month: 11:00 AM</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-bold text-xl mb-4">What to Expect</h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• 60-minute guided tour</li>
                      <li>• Visit to classrooms, labs, and workshops</li>
                      <li>• Q&A with admissions counselor</li>
                    </ul>
                  </div>
                </div>
                <Button
                  asChild
                  className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white"
                >
                  <Link href="/contact">Schedule Your Tour</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-2">What are the best times to visit the campus?</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Weekdays between 9:00 AM and 3:00 PM are ideal for experiencing our campus atmosphere. Scheduled tours
                  are available on Mondays, Wednesdays, and Fridays.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-2">
                  Do I need an appointment to speak with an admissions counselor?
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  While we welcome walk-ins, scheduling an appointment ensures our admissions team can dedicate their
                  full attention to your questions and needs.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-2">How quickly will I receive a response to my inquiry?</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  We strive to respond to all inquiries within 24-48 hours during business days. For urgent matters,
                  please call our main office directly.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <h3 className="font-bold text-lg mb-2">Is there parking available for visitors?</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Yes, we offer free visitor parking for up to 2 hours. Please check in at the security desk upon
                  arrival to receive a visitor's pass.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Take the Next Step?</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Whether you're interested in our academic programs, professional training, or have questions about our
            institution, we're here to help you on your educational journey.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <Link href="/admissions">Apply Now</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              <Link href="/academic-programs">Explore Programs</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
