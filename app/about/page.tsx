import Image from "next/image"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { CheckCircle, Users, BookOpen, Lightbulb, Award, Briefcase, Clock } from "lucide-react"

export const metadata: Metadata = {
  title: "About Us - TISTC | Tobinsco International Science and Technical College",
  description: "Learn about TISTC's mission, vision, and commitment to revolutionizing education through innovation. Discover our history, values, and dedication to excellence in technical education.",
  keywords: "about TISTC, technical college history, educational mission, Port Harcourt education, vocational training",
  openGraph: {
    title: "About Us - TISTC | Tobinsco International Science and Technical College",
    description: "Learn about TISTC's mission, vision, and commitment to revolutionizing education through innovation.",
    images: ['/images/about_img/IMG_1922.JPG'],
  },
}

export default function AboutPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">
              About{" "}
              <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent animate-glow">TISTC</span>
            </h1>
            <p className="text-xl font-semibold text-gray-700 dark:text-gray-300 animate-fade-in-up animate-delay-200">
              Tobinsco International Science and Technical College
            </p>
          </div>

          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2 animate-fade-in-left animate-delay-300">
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-6">
                TISTC is a premier institution committed to delivering top-tier STEM education and industry-relevant
                professional training. We empower students with the knowledge, technical skills, and forward-thinking
                mindset needed to excel in today's fast-evolving global economy.
              </p>
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-8">
                Founded on a vision of innovation, excellence, and inclusivity, TISTC has become a beacon of academic
                and professional advancement across science, engineering, and technology disciplines.
              </p>
            </div>
            <div className="md:w-1/2 animate-fade-in-right animate-delay-400">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl animate-float">
                <Image src="/images/about_img/IMG_1922.JPG" alt="TISTC Campus" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Campus */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row-reverse items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">Our Campus</h2>
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-6">
                Our campus in Abuloma, Port Harcourt features state-of-the-art classrooms, tech-equipped laboratories,
                and interactive workshops that simulate real-world environments, giving students hands-on,
                career-focused learning.
              </p>
            </div>
            <div className="md:w-1/2">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl">
                <Image src="/images/about_img/IMG_1923.JPG" alt="TISTC Campus Facilities" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Vision and Mission */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-none shadow-lg overflow-hidden">
              <div className="h-2 bg-green-600"></div>
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-4 flex items-center">
                  <Lightbulb className="mr-2 h-6 w-6 text-green-600 dark:text-green-400" />
                  Our Vision
                </h2>
                <p className="text-gray-700 dark:text-gray-300 text-lg mb-4">
                  To be a globally recognized institution of excellence in STEM education, fostering innovation,
                  critical thinking, and technological advancement.
                </p>
                <p className="text-gray-700 dark:text-gray-300 text-lg">
                  We envision a future where TISTC graduates are pioneering scientific breakthroughs, solving complex
                  global challenges, and driving the digital and industrial transformation of Africa and beyond.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-none shadow-lg overflow-hidden">
              <div className="h-2 bg-red-600"></div>
              <CardContent className="p-8">
                <h2 className="text-2xl font-bold mb-4 flex items-center">
                  <BookOpen className="mr-2 h-6 w-6 text-red-600 dark:text-red-400" />
                  Our Mission
                </h2>
                <p className="text-gray-700 dark:text-gray-300 text-lg mb-4">
                  To deliver high-quality, accessible education that equips students with practical skills, ethical
                  values, and a passion for discovery in science and technology.
                </p>
                <p className="text-gray-700 dark:text-gray-300 text-lg mb-4">
                  We are committed to building a learning environment that supports:
                </p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-700 dark:text-gray-300">Curiosity and creativity</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-700 dark:text-gray-300">Academic rigor</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-700 dark:text-gray-300">
                      Continuous personal and professional growth
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose TISTC?</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              At TISTC, our promise goes beyond the classroom. We prepare students for real-world success through:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                  <Award size={24} />
                </div>
                <h3 className="font-bold text-xl">Top-Tier Instructors</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Qualified educators and seasoned industry professionals who bring rich insights and experience to your
                learning journey.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4">
                  <Briefcase size={24} />
                </div>
                <h3 className="font-bold text-xl">Job-Ready Curriculum</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                All programs are designed in collaboration with employers to reflect the skills and knowledge demanded
                by today's job market.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                  <Lightbulb size={24} />
                </div>
                <h3 className="font-bold text-xl">Modern Facilities</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Access hands-on training using cutting-edge equipment in our fully equipped labs and workshops.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                  <Users size={24} />
                </div>
                <h3 className="font-bold text-xl">Diverse & Inclusive Community</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                We welcome students from all backgrounds and foster global perspectives through collaboration and
                respect.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400 mr-4">
                  <CheckCircle size={24} />
                </div>
                <h3 className="font-bold text-xl">Industry Partnerships</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Benefit from internships, mentorships, company visits, and job placement support through our strong ties
                with top industry players.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
              <div className="flex items-center mb-4">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                  <Clock size={24} />
                </div>
                <h3 className="font-bold text-xl">Flexible Learning Options</h3>
              </div>
              <p className="text-gray-600 dark:text-gray-300">
                Choose what suits you best: full-time, part-time, hybrid, or online programs tailored to your schedule.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Commitment */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl">
                <Image src="/images/about_img/IMG_1924.JPG" alt="TISTC Modern Campus" fill className="object-cover" />
                <div className="absolute -bottom-6 -right-6 h-24 w-24 bg-red-500 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  STEM
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">
                Our Commitment to{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-red-600 dark:from-green-400 dark:to-red-400">
                  Excellence
                </span>
              </h2>
              <p className="text-gray-700 dark:text-gray-300 mb-4">
                At TISTC, we hold ourselves to the highest standards. Our values include:
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">Quality Education:</span> Continuously evolving curriculum to align
                    with global best practices and technological advancements.
                  </p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">Student Success:</span> Comprehensive academic support, career
                    counselling, and mentorship to guide every student.
                  </p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">Innovation Culture:</span> We nurture inventiveness,
                    problem-solving, and entrepreneurship in every course.
                  </p>
                </div>
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 mt-1 flex-shrink-0" />
                  <p className="text-gray-700 dark:text-gray-300">
                    <span className="font-semibold">Social Responsibility:</span> Instilling a sense of purpose—applying
                    science and tech to improve communities and society at large.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Join Our Community of Innovators</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Whether you're beginning your educational journey, switching careers, or upgrading your skills — TISTC is
            where your future begins.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <Link href="/academic-programs">Explore Our Programs</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              <Link href="/contact">Contact Admissions</Link>
            </Button>
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <Link href="/admissions">Apply Now</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="py-12 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-2">Stay in Touch</h3>
          </div>
          <div className="max-w-3xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">Campus Address:</p>
                <p className="text-gray-600 dark:text-gray-300">
                  242 Abuloma Road, beside Techno Oil, Abuloma, Port Harcourt, Rivers State
                </p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">Email:</p>
                <p className="text-gray-600 dark:text-gray-300"><EMAIL></p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">Phone:</p>
                <p className="text-gray-600 dark:text-gray-300">09160007864</p>
              </div>
            </div>

            <div className="text-center mb-6">
              <p className="text-gray-600 dark:text-gray-300">Follow us on:</p>
              <div className="flex justify-center space-x-4 mt-2">
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  Facebook
                </Link>
                <span className="text-gray-400">|</span>
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  Twitter
                </Link>
                <span className="text-gray-400">|</span>
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  Instagram
                </Link>
                <span className="text-gray-400">|</span>
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  LinkedIn
                </Link>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-2 max-w-md mx-auto">
              <Input type="email" placeholder="Enter your email address" className="bg-gray-50 dark:bg-gray-700" />
              <Button className="bg-green-600 hover:bg-green-700 text-white whitespace-nowrap">Subscribe</Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer Note */}
      <section className="py-4 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4 text-center">
          <p className="text-gray-600 dark:text-gray-400 text-sm">
            © 2025 Tobinsco International Science and Technical College. All rights reserved.
          </p>
          <p className="text-gray-600 dark:text-gray-400 italic mt-1">
            "Revolutionizing Education through Innovation."
          </p>
        </div>
      </section>
    </div>
  )
}
