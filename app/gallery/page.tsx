import type { Metadata } from "next"
import { But<PERSON> } from "@/components/ui/button"
import Gallery from "@/components/gallery"

export const metadata: Metadata = {
  title: "Gallery - TISTC | Campus Photos & Facilities",
  description: "Explore TISTC's modern facilities through our photo gallery. See our classrooms, laboratories, workshops, and campus life at Tobinsco International Science and Technical College.",
  keywords: "TISTC gallery, campus photos, facilities, laboratories, workshops, classrooms, Port Harcourt technical college",
  openGraph: {
    title: "Gallery - TISTC | Campus Photos & Facilities",
    description: "Explore TISTC's modern facilities through our photo gallery. See our classrooms, laboratories, workshops, and campus life.",
    images: ['/images/about_img/IMG_1926.JPG'],
  },
}

export default function GalleryPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">
              Campus{" "}
              <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent animate-glow">
                Gallery
              </span>
            </h1>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-8 animate-fade-in-up animate-delay-200">
              Explore our state-of-the-art facilities, modern laboratories, and vibrant campus life through our
              comprehensive photo gallery.
            </p>
          </div>
        </div>
      </section>

      {/* Gallery */}
      <Gallery showTitle={false} />

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Experience TISTC in Person</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Ready to see our facilities firsthand? Schedule a campus tour and discover what makes TISTC the perfect
            place for your educational journey.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <a href="/contact">Schedule a Tour</a>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10 bg-transparent"
            >
              <a href="/admissions">Apply Now</a>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
