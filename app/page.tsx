import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { ChevronRight, Lightbulb, Award, Briefcase, Users, CheckCircle } from "lucide-react"
import TestimonialsSlider from "@/components/testimonials-slider"
import Gallery from "@/components/gallery"

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 z-0">
          <div className="absolute inset-0 bg-black/40 z-10" />
          <Image
            src="/images/about_img/IMG_1925.JPG"
            alt="TISTC Modern School Building"
            fill
            className="object-cover"
            priority
          />
        </div>
        <div className="container mx-auto px-4 relative z-20 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 animate-fade-in">
            Welcome to <span className="text-green-400 animate-glow">TISTC</span>
          </h1>
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-3xl mx-auto animate-fade-in-up animate-delay-200">
            "Shaping the Future Through Innovation and Excellence"
          </p>
          <p className="text-lg text-white/80 mb-8 max-w-3xl mx-auto animate-fade-in-up animate-delay-300">
            Tobinsco International Science and Technical College (TISTC) is a leading STEM-focused institution dedicated
            to providing world-class technical and academic education in Nigeria. Situated in the heart of Port
            Harcourt, TISTC is committed to raising future innovators, engineers, and entrepreneurs through practical,
            technology-driven education.
          </p>
          <div className="flex flex-wrap justify-center gap-4 animate-fade-in-up animate-delay-400">
            <Button
              asChild
              size="lg"
              className="bg-green-600 hover:bg-green-700 text-white text-lg px-8 py-6 rounded-md shadow-lg"
            >
              <Link href="/academic-programs">Explore Programs</Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/20 text-lg px-8 py-6 rounded-md shadow-lg bg-transparent"
            >
              <Link href="/contact">Request Info</Link>
            </Button>
            <Button
              asChild
              size="lg"
              className="bg-red-600 hover:bg-red-700 text-white text-lg px-8 py-6 rounded-md shadow-lg"
            >
              <Link href="/admissions">Enroll Now</Link>
            </Button>
          </div>
        </div>
        <div className="absolute bottom-10 left-0 right-0 flex justify-center animate-bounce">
          <ChevronRight size={40} className="text-white rotate-90" />
        </div>
      </section>

      {/* Why Choose TISTC */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose TISTC?</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-shadow overflow-hidden border-none animate-fade-in-up animate-delay-100">
              <div className="h-2 bg-green-500"></div>
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                    <Lightbulb size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Innovation-Driven</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  A modern curriculum that integrates STEM, creativity, and real-world applications.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-shadow overflow-hidden border-none animate-fade-in-up animate-delay-200">
              <div className="h-2 bg-blue-500"></div>
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                    <Award size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Academic Excellence</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  Core subjects that form a strong foundation for academic success and global relevance.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-shadow overflow-hidden border-none animate-fade-in-up animate-delay-300">
              <div className="h-2 bg-purple-500"></div>
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 mr-4">
                    <Briefcase size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Industry-Ready</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  Technical and vocational programs built on practical experience and hands-on learning.
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-shadow overflow-hidden border-none animate-fade-in-up animate-delay-400">
              <div className="h-2 bg-green-500"></div>
              <CardContent className="pt-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                    <Users size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Inclusive Community</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300">
                  A diverse, safe, and collaborative learning environment.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Academic Programs */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Academic Programs</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Core Academic Subjects (Compulsory for All Students)
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-12">
            {[
              "English Language",
              "Mathematics",
              "Physics",
              "Chemistry",
              "Biology",
              "Civic Education",
              "Computer Studies / ICT",
              "Technical Drawing",
              "Physical & Health Education (PHE)",
            ].map((subject, index) => (
              <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center">
                <p className="font-medium text-gray-800 dark:text-gray-200">{subject}</p>
              </div>
            ))}
          </div>

          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-4">Technical & Vocational Trade Specializations</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <h4 className="text-xl font-bold mb-4">Engineering & Technical Fields</h4>
                <ul className="space-y-2">
                  {[
                    "Mechanical Engineering Craft",
                    "Engineering Science",
                    "Electrical Installation & Maintenance",
                    "Computer Craft Studies",
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700 dark:text-gray-300">{item}</span>
                    </li>
                  ))}
                </ul>
                <div className="ml-7 mt-2">
                  <p className="text-gray-700 dark:text-gray-300 font-medium">Computer Craft Studies includes:</p>
                  <ul className="space-y-1 mt-1">
                    {[
                      "Hardware Maintenance",
                      "Software Installation",
                      "Networking Basics",
                      "Web Design",
                      "Programming (Python, C++)",
                    ].map((item, index) => (
                      <li key={index} className="flex items-start">
                        <span className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-2"></span>
                        <span className="text-gray-600 dark:text-gray-400">{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <h4 className="text-xl font-bold mb-4">Specialized Skills & Creativity</h4>
                <ul className="space-y-2">
                  {["Welding & Fabrication", "Fashion Design & Garment Making"].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                      <span className="text-gray-700 dark:text-gray-300">{item}</span>
                    </li>
                  ))}
                </ul>

                <h4 className="text-xl font-bold mb-4 mt-6">Emerging Fields</h4>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700 dark:text-gray-300">Social Science & Civic Leadership</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <div className="text-center">
            <Button asChild size="lg" className="bg-green-600 hover:bg-green-700 text-white px-8">
              <Link href="/academic-programs">View All Programs</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Gallery Preview */}
      <Gallery showTitle={true} maxImages={6} />

      {/* Professional Training */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Professional Training & Corporate Solutions</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              We offer customized programs for organizations seeking to upskill their workforce. Our team of industry
              experts delivers training in:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {[
              "Data Analytics",
              "Business Intelligence",
              "Computer Networking",
              "Tech-Driven Project Management",
              "Digital Literacy for Teams",
            ].map((item, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 p-5 rounded-lg shadow-md text-center">
                <p className="font-medium text-gray-800 dark:text-gray-200">{item}</p>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Button asChild size="lg" className="bg-red-600 hover:bg-red-700 text-white px-8">
              <Link href="/professional-training">Partner With Us</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Campus Facilities */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">Modern Campus & Facilities</h2>
              <p className="text-gray-700 dark:text-gray-300 mb-6 text-lg">
                TISTC boasts modern laboratories, high-speed internet access, equipped workshops, and comfortable
                learning environments — all designed to support practical and immersive education.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button asChild className="bg-green-600 hover:bg-green-700 text-white">
                  <Link href="/about">Learn More About Us</Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  className="border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400 dark:hover:bg-green-900/20 bg-transparent"
                >
                  <Link href="/contact">Book a Campus Tour</Link>
                </Button>
              </div>
            </div>
            <div className="md:w-1/2 relative">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl">
                <Image src="/images/about_img/IMG_1926.JPG" alt="TISTC Modern Campus" fill className="object-cover" />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 p-6">
                  <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                    State-of-the-art Facilities
                  </span>
                </div>
              </div>
              <div className="absolute -bottom-6 -right-6 h-24 w-24 bg-green-600 rounded-full flex items-center justify-center text-white font-bold text-xl shadow-lg">
                STEM
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <TestimonialsSlider />

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Take the First Step Toward a Brighter Future</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Whether you're a student, a parent, or a professional, TISTC welcomes you to be part of our innovative
            academic community.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100 px-8">
              <Link href="/admissions">Apply Now</Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10 px-8 bg-transparent"
            >
              <Link href="/contact">Request Information</Link>
            </Button>
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100 px-8">
              <Link href="/contact">Book a Campus Tour</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-12 bg-gray-100 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold mb-2">Stay Connected</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Subscribe to get the latest updates, admission notices, and training offers.
              </p>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Input type="email" placeholder="Your Email Address" className="bg-white dark:bg-gray-700" />
              <Button className="bg-green-600 hover:bg-green-700 text-white whitespace-nowrap">Subscribe</Button>
            </div>
            <div className="text-center mt-6">
              <p className="text-gray-600 dark:text-gray-300 text-sm">Follow Us:</p>
              <div className="flex justify-center space-x-4 mt-2">
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  Facebook
                </Link>
                <span className="text-gray-400">|</span>
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  Twitter
                </Link>
                <span className="text-gray-400">|</span>
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  Instagram
                </Link>
                <span className="text-gray-400">|</span>
                <Link
                  href="#"
                  className="text-gray-600 hover:text-green-600 dark:text-gray-400 dark:hover:text-green-400"
                >
                  LinkedIn
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>


    </div>
  )
}
