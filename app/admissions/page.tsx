import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { CheckCircle, Calendar, Clock, HelpCircle } from "lucide-react"

export default function AdmissionsPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Admissions at{" "}
              <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent">TISTC</span>
            </h1>
            <p className="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-4">
              Your Future Begins Here — From JSS1 to Professional Success
            </p>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-8">
              At Tobinsco International Science and Technical College (TISTC), we welcome students at all stages of
              their educational journey — from Junior Secondary School (JSS1) to technical, vocational, and professional
              training programs.
            </p>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-8">
              Our admissions process is designed to be straightforward, inclusive, and supportive, ensuring students and
              parents feel confident every step of the way.
            </p>
          </div>
        </div>
      </section>

      {/* Admissions Streams */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Admissions Streams at a Glance</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-green-500 to-green-600"></div>
              <CardContent className="p-6">
                <h3 className="font-bold text-xl mb-4">Junior Secondary School (JSS1–JSS3)</h3>
                <ul className="space-y-2 mb-4">
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Ages: 10–13+</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">
                      Focus: Strong academic foundation in core subjects + ICT exposure
                    </span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">
                      Entry Requirement: Completion of Primary School (Primary 6), passing entrance assessment
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-red-500 to-red-600"></div>
              <CardContent className="p-6">
                <h3 className="font-bold text-xl mb-4">Senior Secondary School (SS1–SS3)</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-3">Technical/Vocational Tracks in:</p>
                <ul className="space-y-2 mb-4">
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Electrical Installation</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Mechanical Craft</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Computer Craft</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Fashion Design</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Welding & Fabrication</span>
                  </li>
                </ul>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Entry Requirement: JSS3 Completion, Basic Education Certificate (BECE), TISTC internal screening
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-600"></div>
              <CardContent className="p-6">
                <h3 className="font-bold text-xl mb-4">Professional Training Programs</h3>
                <p className="text-gray-600 dark:text-gray-300 mb-3">Short-term certifications in:</p>
                <ul className="space-y-2 mb-4">
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Data Analytics</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Web & Mobile Development</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Cybersecurity</span>
                  </li>
                  <li className="flex items-start">
                    <span className="h-2 w-2 rounded-full bg-blue-500 mr-2 mt-2"></span>
                    <span className="text-gray-600 dark:text-gray-300">Python Programming</span>
                  </li>
                </ul>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Entry Requirement: Basic literacy, age 16+, specific prerequisites per course
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* How to Apply */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">How to Apply</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-green-500 to-green-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 flex items-center justify-center font-bold text-xl">
                    1
                  </div>
                  <h3 className="font-bold text-xl ml-4">Submit Your Application</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">Fill out the online or paper application form:</p>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Personal information</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Previous school attended (for JSS/SS)</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">
                      Program of interest (for professional courses)
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-red-500 to-red-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 flex items-center justify-center font-bold text-xl">
                    2
                  </div>
                  <h3 className="font-bold text-xl ml-4">Submit Required Documents</h3>
                </div>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">
                      BECE or Primary School Leaving Certificate (JSS1–SS1)
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Birth certificate or National ID</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">Passport photograph</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">
                      Transcript or testimonial (if transferring)
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 flex items-center justify-center font-bold text-xl">
                    3
                  </div>
                  <h3 className="font-bold text-xl ml-4">Entrance Assessment</h3>
                </div>
                <ul className="space-y-2">
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">
                      For JSS1–SS1: Written entrance exam and interview
                    </span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0" />
                    <span className="text-gray-600 dark:text-gray-300">
                      For Professional programs: Skill screening or interview (for specialized courses)
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none">
              <div className="h-2 bg-gradient-to-r from-purple-500 to-purple-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="flex-shrink-0 h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 flex items-center justify-center font-bold text-xl">
                    4
                  </div>
                  <h3 className="font-bold text-xl ml-4">Admission Offer</h3>
                </div>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  Receive admission letter and payment guidelines within 1–2 weeks after assessment.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Requirements Table */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">General Admission Requirements</h2>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="overflow-x-auto">
              <table className="w-full bg-white dark:bg-gray-800 rounded-lg shadow-md">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Level
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                      Requirements
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                      JSS1
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      Primary 6 certificate, Birth Certificate, Entrance Exam
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                      SS1
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      JSS3 certificate or BECE, Transcript, Screening Test
                    </td>
                  </tr>
                  <tr>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                      Professional Training
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                      Age 16+, Basic literacy, Computer familiarity, Program-specific screening
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* Important Dates */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Important Dates</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <div className="h-2 bg-gradient-to-r from-green-500 to-green-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 mr-4">
                    <Calendar size={24} />
                  </div>
                  <h3 className="font-bold text-xl">2025–2026 Academic Year (JSS & SS)</h3>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <Clock className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-semibold">Application Deadline</p>
                      <p className="text-gray-600 dark:text-gray-300">July 15, 2025</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Clock className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-semibold">Entrance Exam</p>
                      <p className="text-gray-600 dark:text-gray-300">August 3 & August 10, 2025</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Clock className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-semibold">Orientation</p>
                      <p className="text-gray-600 dark:text-gray-300">August 25–26, 2025</p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <Clock className="h-5 w-5 text-green-600 dark:text-green-400 mr-2 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-semibold">Classes Begin</p>
                      <p className="text-gray-600 dark:text-gray-300">September 2, 2025</p>
                    </div>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <div className="h-2 bg-gradient-to-r from-blue-500 to-blue-600"></div>
              <CardContent className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 mr-4">
                    <Calendar size={24} />
                  </div>
                  <h3 className="font-bold text-xl">Professional Training Start Dates</h3>
                </div>
                <ul className="space-y-3">
                  <li className="flex items-start">
                    <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-semibold">Next Cohort Start Dates</p>
                      <p className="text-gray-600 dark:text-gray-300">June 1, 2025</p>
                      <p className="text-gray-600 dark:text-gray-300">September 15, 2025</p>
                      <p className="text-gray-600 dark:text-gray-300">January 5, 2026</p>
                    </div>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Apply Now Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Apply Now</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Ready to join TISTC? Start your application now — whether you're enrolling into JSS1, Senior Secondary, or
              Professional Certification:
            </p>
          </div>

          <div className="flex flex-col sm:flex-row justify-center gap-4 mb-12">
            <Button asChild size="lg" className="bg-green-600 hover:bg-green-700 text-white">
              <Link href="/contact">Apply for JSS1 / SS1 Admission</Link>
            </Button>
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
              <Link href="/professional-training">Apply for a Professional Course</Link>
            </Button>
            <Button
              asChild
              size="lg"
              variant="outline"
              className="border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400 dark:hover:bg-green-900/20"
            >
              <Link href="/contact">Request Information</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Frequently Asked Questions</h2>
          </div>

          <div className="max-w-3xl mx-auto space-y-6">
            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <div className="flex items-start">
                  <HelpCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-bold text-lg mb-2">What age is appropriate for JSS1?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Most JSS1 applicants are 10–13 years old. Completion of primary education is required.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <div className="flex items-start">
                  <HelpCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-bold text-lg mb-2">Do you offer boarding facilities?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Yes. On-campus boarding is available for JSS and SS students. Contact Student Services for
                      details.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <div className="flex items-start">
                  <HelpCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-bold text-lg mb-2">Is TISTC government-approved?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Yes. We are duly registered with the Ministry of Education and accredited for all academic and
                      technical programs.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <div className="flex items-start">
                  <HelpCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-bold text-lg mb-2">
                      Can my child continue from SS3 into your professional courses?
                    </h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Absolutely. SS3 graduates are encouraged to pursue professional training or higher technical
                      certifications with us.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white dark:bg-gray-800 shadow-md border-none">
              <CardContent className="p-6">
                <div className="flex items-start">
                  <HelpCircle className="h-6 w-6 text-green-600 dark:text-green-400 mr-3 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="font-bold text-lg mb-2">Are scholarships available?</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Yes. TISTC offers merit-based and need-based scholarships. Apply early to be considered.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Admissions */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Contact the Admissions Office</h2>
          </div>

          <div className="max-w-3xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center">
                <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">Address:</p>
                <p className="text-gray-600 dark:text-gray-300">
                  242 Abuloma Road, beside Techno Oil, Abuloma, Port Harcourt, Rivers State
                </p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">Phone:</p>
                <p className="text-gray-600 dark:text-gray-300">09160007864</p>
              </div>
              <div className="text-center">
                <p className="font-semibold text-gray-800 dark:text-gray-200 mb-1">Email:</p>
                <p className="text-gray-600 dark:text-gray-300"><EMAIL> | <EMAIL></p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Begin Your Educational Journey?</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Take the first step towards a rewarding career in science and technology. Apply to TISTC today and be part
            of our community of future leaders and innovators.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <Link href="/contact">Apply Now</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              <Link href="/contact">Contact Us</Link>
            </Button>
          </div>
          <div className="mt-6">
            <p className="text-lg italic">"Revolutionizing Education from Foundation to Future."</p>
          </div>
        </div>
      </section>
    </div>
  )
}
