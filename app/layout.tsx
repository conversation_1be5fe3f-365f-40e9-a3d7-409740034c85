import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import Navbar from "@/components/navbar"
import Footer from "@/components/footer"
import EnrollButton from "@/components/enroll-button"
import ScrollToTop from "@/components/scroll-to-top"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "TISTC - Tobinsco International Science and Technical College",
  description: "Revolutionizing Education through Innovation - Premier technical and vocational education in Port Harcourt, Rivers State",
  keywords: "technical college, vocational education, Port Harcourt, Rivers State, engineering, fashion design, computer science, welding, fabrication",
  authors: [{ name: "Tobinsco International Science and Technical College" }],
  creator: "TIS<PERSON>",
  publisher: "TISTC",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://tistc.edu.ng'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "TISTC - Tobinsco International Science and Technical College",
    description: "Revolutionizing Education through Innovation - Premier technical and vocational education in Port Harcourt, Rivers State",
    url: 'https://tistc.edu.ng',
    siteName: 'TISTC',
    images: [
      {
        url: '/images/tobinsco-logo.png',
        width: 800,
        height: 600,
        alt: 'TISTC Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "TISTC - Tobinsco International Science and Technical College",
    description: "Revolutionizing Education through Innovation - Premier technical and vocational education in Port Harcourt, Rivers State",
    images: ['/images/tobinsco-logo.png'],
  },
  icons: {
    icon: '/images/tobinsco-fav.png',
    shortcut: '/images/tobinsco-fav.png',
    apple: '/images/tobinsco-fav.png',
  },
  manifest: '/manifest.json',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <ScrollToTop />
          <div className="flex min-h-screen flex-col">
            <Navbar />
            <main className="flex-1">{children}</main>
            <EnrollButton />
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
