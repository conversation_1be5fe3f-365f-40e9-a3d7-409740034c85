import Image from "next/image"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { BookO<PERSON>, Clock, Award, Users } from "lucide-react"

const programs = [
  {
    id: 1,
    title: "Electrical Engineering",
    description:
      "Dive into the study of electrical systems, circuit theory, power generation, and electronics with hands-on lab experience.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "4 Years",
    level: "Undergraduate",
    type: "Featured Program",
    highlights: [
      "Circuit Design & Analysis",
      "Power Systems & Energy Conversion",
      "Microprocessors & Embedded Systems",
      "Control Systems & Automation",
    ],
  },
  {
    id: 2,
    title: "Mechanical Engineering",
    description:
      "A robust program covering mechanics, heat transfer, materials science, and modern manufacturing techniques.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "4 Years",
    level: "Undergraduate",
    type: "Featured Program",
    highlights: [
      "Mechanical System Design",
      "Thermodynamics & Heat Transfer",
      "Materials Science",
      "Robotics & Manufacturing Automation",
    ],
  },
  {
    id: 3,
    title: "Computer Science",
    description:
      "Master the art of software development, algorithms, and emerging technologies like AI and data science.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "4 Years",
    level: "Undergraduate",
    type: "Featured Program",
    highlights: [
      "Programming (Python, C++, Java)",
      "Data Structures & Algorithms",
      "Databases & Systems Design",
      "Artificial Intelligence & Machine Learning",
    ],
  },
  {
    id: 4,
    title: "Information Technology",
    description: "Focused on IT systems, security, cloud platforms, and network management.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "4 Years",
    level: "Undergraduate",
    type: "Featured Program",
    highlights: [
      "Network Administration & Design",
      "Cybersecurity & Risk Management",
      "Cloud Computing & Virtualization",
      "IT Support & Project Management",
    ],
  },
  {
    id: 5,
    title: "Fashion & Design",
    description:
      "Where creativity meets sustainability—merge fashion design with CAD tools and eco-conscious practices.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "3 Years",
    level: "Undergraduate",
    type: "Featured Program",
    highlights: [
      "Fashion Illustration & Design",
      "Textile Science & Technology",
      "Computer-Aided Design (CAD)",
      "Ethical & Sustainable Fashion",
    ],
  },
  {
    id: 6,
    title: "General Education",
    description:
      "A well-rounded education in liberal arts, sciences, and digital literacy—ideal for academic advancement or career shifts.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "2 Years",
    level: "Foundation Program",
    type: "Foundation Program",
    highlights: [
      "Critical Thinking & Communication",
      "Science & Math Foundations",
      "Social Sciences & Humanities",
      "Digital Literacy & Emerging Technologies",
    ],
  },
]

export default function AcademicProgramsPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Where{" "}
              <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent">
                Innovation
              </span>{" "}
              Meets Education
            </h1>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-8">
              TISTC offers a comprehensive range of academic programs designed to equip students with the knowledge,
              skills, and real-world experience necessary to thrive in today's dynamic and tech-driven job market.
            </p>
            <p className="text-xl font-semibold text-gray-800 dark:text-gray-200 italic">
              "We don't just teach—we prepare you for success."
            </p>
          </div>
        </div>
      </section>

      {/* Programs Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Academic Excellence Across Disciplines</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Explore our fully accredited undergraduate and associate degree programs. Each curriculum blends theory,
              practical labs, and industry exposure, ensuring you're job-ready from day one.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {programs.map((program) => (
              <Card
                key={program.id}
                className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none"
              >
                <div className="relative h-48">
                  <Image src={program.image || "/placeholder.svg"} alt={program.title} fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 p-4">
                    <h3 className="text-white font-bold text-xl">{program.title}</h3>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center text-gray-600 dark:text-gray-300">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{program.duration}</span>
                    </div>
                    <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {program.type}
                    </span>
                    <span className="bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium">
                      {program.level}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{program.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Program Highlights:</h4>
                    <ul className="space-y-1">
                      {program.highlights.map((highlight, index) => (
                        <li key={index} className="flex items-start">
                          <span className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-2"></span>
                          <span className="text-gray-600 dark:text-gray-300">{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Button
                    asChild
                    variant="outline"
                    className="w-full border-green-600 text-green-600 hover:bg-green-50 dark:border-green-400 dark:text-green-400 dark:hover:bg-green-900/20"
                  >
                    <Link href="/admissions">Learn More</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Program Features */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Key Features of All Programs</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 rounded-full bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400">
                  <BookOpen size={24} />
                </div>
              </div>
              <h3 className="font-bold text-xl mb-2">Comprehensive Curriculum</h3>
              <p className="text-gray-600 dark:text-gray-300">Integrating theory with real-world applications.</p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400">
                  <Award size={24} />
                </div>
              </div>
              <h3 className="font-bold text-xl mb-2">Expert Faculty</h3>
              <p className="text-gray-600 dark:text-gray-300">Learn from seasoned educators and industry veterans.</p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400">
                  <Users size={24} />
                </div>
              </div>
              <h3 className="font-bold text-xl mb-2">Small Class Sizes</h3>
              <p className="text-gray-600 dark:text-gray-300">
                Enjoy personalized mentorship and classroom engagement.
              </p>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400">
                  <BookOpen size={24} />
                </div>
              </div>
              <h3 className="font-bold text-xl mb-2">Practical Training</h3>
              <p className="text-gray-600 dark:text-gray-300">Labs, workshops, projects, and internships included.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Admission Process */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">
                Admission{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-red-600 dark:from-green-400 dark:to-red-400">
                  Process
                </span>
              </h2>
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-6">
                Getting started at TISTC is easy and supportive. Here's how to begin your journey:
              </p>
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-600 text-white flex items-center justify-center font-bold">
                    1
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-lg">Submit Application</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Complete our online form with personal and academic details.
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-600 text-white flex items-center justify-center font-bold">
                    2
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-lg">Upload Documents</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Provide necessary credentials— ID, and passport photo.
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-600 text-white flex items-center justify-center font-bold">
                    3
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-lg">Entrance Assessment</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Depending on your chosen program, you may take a test.
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-8 w-8 rounded-full bg-green-600 text-white flex items-center justify-center font-bold">
                    4
                  </div>
                  <div className="ml-4">
                    <h3 className="font-semibold text-lg">Receive Admission Offer</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Get your admission decision within 2–4 weeks after completing all steps.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="md:w-1/2">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl">
                <Image src="/images/modern-school.png" alt="Students in classroom" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Begin?</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Launch your academic journey with confidence. Whether you're joining fresh from high school or switching
            careers, TISTC has a place for you.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white">
              <Link href="/admissions">Apply Now</Link>
            </Button>
            <Button asChild size="lg" className="bg-yellow-600 hover:bg-yellow-700 text-white">
              <Link href="/contact">Request More Information</Link>
            </Button>
            <Button asChild size="lg" className="bg-green-500 hover:bg-green-600 text-white">
              <Link href="/admissions">Enroll Today</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
