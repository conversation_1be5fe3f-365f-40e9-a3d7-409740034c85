import Image from "next/image"
import Link from "next/link"
import type { <PERSON>ada<PERSON> } from "next"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Clock, Award, Users, Briefcase, Building, GraduationCap } from "lucide-react"

export const metadata: Metadata = {
  title: "Professional Training - TISTC | Skills Development & Certification",
  description: "Advance your career with TISTC's professional training programs. Data Analytics, Digital Marketing, Project Management, Welding, and more. Industry-recognized certifications.",
  keywords: "professional training, data analytics, digital marketing, project management, welding certification, skills development, Port Harcourt",
  openGraph: {
    title: "Professional Training - TISTC | Skills Development & Certification",
    description: "Advance your career with TISTC's professional training programs. Industry-recognized certifications and practical skills development.",
    images: ['/images/about_img/IMG_1924.JPG'],
  },
}

const courses = [
  {
    id: 1,
    title: "Data Analytics",
    description: "Master the core of data science: from data cleaning to visualization and basic predictive modeling.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "12 Weeks",
    level: "Intermediate",
    highlights: [
      "Data collection & preprocessing",
      "Statistical analysis & interpretation",
      "Power BI & Tableau dashboards",
      "Introduction to machine learning",
    ],
  },
  {
    id: 2,
    title: "Cybersecurity",
    description:
      "Learn how to secure digital assets, defend networks, and respond to threats with professional-grade tools.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "16 Weeks",
    level: "Advanced",
    highlights: [
      "Network security & firewall configuration",
      "Ethical hacking & penetration testing",
      "Incident detection & response",
      "Cybersecurity frameworks (NIST, ISO, etc.)",
    ],
  },
  {
    id: 3,
    title: "Full-Stack Web Development",
    description: "Become a versatile web developer with in-depth training in front-end and back-end technologies.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "24 Weeks",
    level: "Beginner to Advanced",
    highlights: [
      "HTML, CSS, JavaScript essentials",
      "Front-end frameworks (React, Vue)",
      "Back-end (Node.js, Express)",
      "Database management & APIs",
    ],
  },
  {
    id: 4,
    title: "Python Programming",
    description: "From beginner to practical applications in automation and data science using Python.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "10 Weeks",
    level: "Beginner to Intermediate",
    highlights: [
      "Python syntax & core concepts",
      "Loops, functions, and data structures",
      "Web scraping & task automation",
      "Data analysis with Pandas & NumPy",
    ],
  },
  {
    id: 5,
    title: "Product Design (UI/UX)",
    description:
      "Build intuitive, user-friendly digital experiences with modern design tools and human-centered methodologies.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "14 Weeks",
    level: "Intermediate",
    highlights: [
      "User research & persona development",
      "Wireframing & rapid prototyping",
      "Figma & Adobe XD mastery",
      "Usability testing & iteration",
    ],
  },
  {
    id: 6,
    title: "Mobile App Development",
    description: "Build and deploy cross-platform and native mobile apps for iOS and Android ecosystems.",
    image: "/placeholder.svg?height=400&width=600",
    duration: "16 Weeks",
    level: "Intermediate",
    highlights: [
      "React Native for cross-platform development",
      "Swift for native iOS",
      "Kotlin for native Android",
      "App publishing & updates",
    ],
  },
]

const benefits = [
  {
    title: "Industry-Recognized Certification",
    description: "Prepare for exams and certifications that validate your skills and enhance employability",
    icon: Award,
    color: "green",
  },
  {
    title: "Hands-on Learning",
    description: "Practical, project-based learning using modern tools and platforms",
    icon: Briefcase,
    color: "red",
  },
  {
    title: "Expert Instructors",
    description: "Learn from seasoned professionals with real-world experience",
    icon: Users,
    color: "blue",
  },
  {
    title: "Flexible Schedules",
    description: "Full-time, part-time, weekend, and online formats available",
    icon: Clock,
    color: "purple",
  },
  {
    title: "Industry Network",
    description: "Get access to internships, job opportunities, and guest sessions from tech leaders",
    icon: Building,
    color: "yellow",
  },
  {
    title: "Career Support",
    description: "Resume reviews, interview prep, and job placement assistance included",
    icon: GraduationCap,
    color: "green",
  },
]

export default function ProfessionalTrainingPage() {
  return (
    <div className="pt-20">
      {/* Hero Section */}
      <section className="relative py-20 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 animate-fade-in">
              Powering Your Future in the{" "}
              <span className="bg-gradient-to-r from-green-500 to-green-600 bg-clip-text text-transparent animate-glow">
                Digital Economy
              </span>
            </h1>
            <p className="text-gray-700 dark:text-gray-300 text-lg mb-8 animate-fade-in-up animate-delay-200">
              TISTC's Professional IT Training programs are designed to equip individuals with industry-relevant,
              job-ready skills that employers demand. Whether you're looking to change careers, upskill, or specialize,
              we provide the tools, training, and support you need to thrive in today's fast-paced tech world.
            </p>
          </div>
        </div>
      </section>

      {/* Courses Section */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Future-Ready Training Programs</h2>
            <p className="text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Explore our curated selection of practical, hands-on courses for individuals and organizations:
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {courses.map((course) => (
              <Card
                key={course.id}
                className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none"
              >
                <div className="relative h-48">
                  <Image src={course.image || "/placeholder.svg"} alt={course.title} fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 p-4">
                    <h3 className="text-white font-bold text-xl">{course.title}</h3>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center text-gray-600 dark:text-gray-300">
                      <Clock className="h-4 w-4 mr-1" />
                      <span>{course.duration}</span>
                    </div>
                    <span className="bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 px-3 py-1 rounded-full text-sm font-medium">
                      {course.level}
                    </span>
                  </div>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{course.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">Course Highlights:</h4>
                    <ul className="space-y-1">
                      {course.highlights.map((highlight, index) => (
                        <li key={index} className="flex items-start">
                          <span className="h-2 w-2 rounded-full bg-red-500 mr-2 mt-2"></span>
                          <span className="text-gray-600 dark:text-gray-300">{highlight}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Button
                    asChild
                    variant="outline"
                    className="w-full border-red-600 text-red-600 hover:bg-red-50 dark:border-red-400 dark:text-red-400 dark:hover:bg-red-900/20"
                  >
                    <Link href="/contact">Enroll Now</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Corporate & Government Training */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center gap-12">
            <div className="md:w-1/2">
              <h2 className="text-3xl font-bold mb-6">
                Corporate & Government{" "}
                <span className="bg-clip-text text-transparent bg-gradient-to-r from-green-600 to-red-600 dark:from-green-400 dark:to-red-400">
                  Training Solutions
                </span>
              </h2>
              <p className="text-gray-700 dark:text-gray-300 text-lg mb-6">
                We partner with organizations to develop customized, results-driven training that meets specific
                workforce development needs.
              </p>
              <div className="space-y-4 mb-8">
                <div className="flex items-start">
                  <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4 flex-shrink-0">
                    <Building size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Customized Training</h3>
                    <p className="text-gray-600 dark:text-gray-300">Aligned with organizational goals and skill gaps</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4 flex-shrink-0">
                    <Users size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Flexible Delivery</h3>
                    <p className="text-gray-600 dark:text-gray-300">On-site, online, or blended learning models</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4 flex-shrink-0">
                    <GraduationCap size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Expert Facilitators</h3>
                    <p className="text-gray-600 dark:text-gray-300">Courses led by top industry practitioners</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="p-2 rounded-full bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400 mr-4 flex-shrink-0">
                    <Briefcase size={20} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Practical Focus</h3>
                    <p className="text-gray-600 dark:text-gray-300">
                      Real-world scenarios, hands-on projects, and case studies
                    </p>
                  </div>
                </div>
              </div>
              <Button
                asChild
                className="bg-gradient-to-r from-green-600 to-red-600 hover:from-green-700 hover:to-red-700 text-white"
              >
                <Link href="/contact">Request Corporate Training</Link>
              </Button>
            </div>
            <div className="md:w-1/2">
              <div className="relative h-[400px] w-full rounded-lg overflow-hidden shadow-xl">
                <Image src="/images/about_img/IMG_1927.JPG" alt="Corporate training session" fill className="object-cover" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Training Benefits */}
      <section className="py-16 bg-white dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Why Choose TISTC's IT Training Programs?</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => {
              const Icon = benefit.icon
              const colorClasses = {
                green:
                  "from-green-500 to-green-600 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400",
                red: "from-red-500 to-red-600 bg-red-100 dark:bg-red-900/30 text-red-600 dark:text-red-400",
                blue: "from-blue-500 to-blue-600 bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400",
                purple:
                  "from-purple-500 to-purple-600 bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400",
                yellow:
                  "from-yellow-500 to-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-600 dark:text-yellow-400",
              }

              return (
                <Card
                  key={index}
                  className="bg-white dark:bg-gray-800 shadow-md hover:shadow-xl transition-all hover:-translate-y-1 overflow-hidden border-none"
                >
                  <div
                    className={`h-2 bg-gradient-to-r ${colorClasses[benefit.color]?.split(" ")[0]} ${colorClasses[benefit.color]?.split(" ")[1]}`}
                  ></div>
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <div
                        className={`p-3 rounded-full ${colorClasses[benefit.color]?.split(" ").slice(2).join(" ")} mr-4`}
                      >
                        <Icon size={24} />
                      </div>
                      <h3 className="font-bold text-xl">{benefit.title}</h3>
                    </div>
                    <p className="text-gray-600 dark:text-gray-300">{benefit.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-green-600 to-green-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Advance Your Career?</h2>
          <p className="text-xl max-w-3xl mx-auto mb-8">
            Whether you're just starting out or looking to specialize, our professional training programs are your
            launchpad into a tech-driven future.
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
              <Link href="/contact">Enroll Now</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
              <Link href="/contact">Request More Information</Link>
            </Button>
          </div>
          <div className="mt-6">
            <p className="text-lg font-semibold">Join the TISTC Tech Community</p>
          </div>
        </div>
      </section>
    </div>
  )
}
